# 颜色范围搜索功能使用说明

## 功能概述

颜色范围搜索功能允许用户输入一个颜色值，通过外部 `tt` 工具生成正则表达式，在 `data.json` 文件中搜索匹配的颜色，并按 `target` 分组显示结果。

## UI 设计风格

新的颜色范围搜索界面采用了与设备选择界面一致的现代化 macOS 风格：

- **毛玻璃效果**：使用 `backdrop-filter: blur(20px)` 实现优雅的背景模糊
- **半透明设计**：所有界面元素都采用半透明白色背景
- **微妙阴影**：使用多层阴影营造深度感
- **流畅动画**：所有交互都有平滑的过渡效果
- **系统字体**：使用 SF Pro Text 等系统字体确保一致性

## 使用方法

### 1. 基本搜索流程

1. **打开颜色搜索窗口**
   - 在 Mini Edit Pro 中打开颜色搜索功能

2. **输入颜色值**
   - 在右上角的输入框中输入颜色值
   - 支持格式：`#hex` 或 `hex` + 可选的空格和数字(1-30)
   - 示例：
     - `#D84059` (标准6位十六进制)
     - `D84059` (无#号，系统自动添加)
     - `#D84059 20` (颜色值 + 范围值20)
     - `ABC 15` (3位格式 + 范围值15)
     - `#D84059FF 10` (8位格式 + 范围值10，自动截取前6位)
   - 范围值必须在1-30之间，用于控制颜色匹配的偏差范围

3. **执行搜索**
   - 点击搜索按钮 🔍 或按回车键
   - 系统会调用 `tt` 命令生成颜色范围正则表达式
   - 在 `data.json` 中搜索匹配的颜色

4. **查看结果**
   - 搜索结果会在弹出窗口中按 `target` 分组显示
   - 每个分组显示该 `target` 下匹配的颜色数量

### 2. 批量操作

1. **选择目标分组**
   - 在结果窗口中点击任意 `target` 分组

2. **确认操作**
   - 系统会显示确认对话框
   - 显示将要处理的颜色数量

3. **执行批量命令**
   - 确认后系统会收集该 `target` 下所有颜色的 `name` 值
   - 自动执行 `tt "name1,name2,name3,..." 10 2` 命令

## 技术实现

### 模块架构

```
color_range_search.ts          # 核心搜索逻辑
├── regex_processor.ts         # tt命令调用和正则表达式处理
├── search_results_display.ts  # 结果展示UI组件
├── color_range_search_ui.ts   # 输入框UI和事件处理
└── color_range_search_test.ts # 功能测试模块
```

### 主要功能模块

1. **ColorRangeSearchManager**
   - 管理整个搜索流程
   - 处理颜色输入验证
   - 协调各个子模块

2. **RegexProcessor**
   - 调用外部 `tt` 命令
   - 解析命令输出提取正则表达式
   - 执行批量操作命令

3. **SearchResultsDisplay**
   - 创建结果展示窗口
   - 按 `target` 分组显示
   - 处理批量操作交互

4. **ColorRangeSearchUI**
   - 初始化输入框和按钮
   - 处理用户输入验证
   - 绑定事件监听器

### 数据流程

```
用户输入颜色值
    ↓
清理和验证输入
    ↓
调用 tt 命令生成正则表达式
    ↓
读取 data.json 文件
    ↓
使用正则表达式匹配颜色
    ↓
按 target 分组结果
    ↓
显示结果窗口
    ↓
用户点击 target 执行批量操作
```

## 错误处理

### 常见错误及解决方案

1. **"请输入有效的颜色值格式"**
   - 支持格式：`#hex` 或 `hex` + 可选的空格和数字(1-30)
   - 颜色部分只能包含十六进制字符（0-9, A-F）
   - 范围值必须是1-30之间的整数
   - 示例：`#D84059 20`、`ABC 15`、`D84059`

2. **"生成正则表达式失败"**
   - 检查 `tt` 命令是否正确安装
   - 确保 `tt` 命令在系统 PATH 中

3. **"data.json路径未初始化"**
   - 确保颜色数据库路径正确配置
   - 检查 `data.json` 文件是否存在

4. **"未找到匹配的颜色"**
   - 尝试其他颜色值
   - 检查 `data.json` 文件内容是否正确

## 开发和调试

### 测试功能

在开发环境中，系统会自动运行测试：

```javascript
// 手动运行测试
window.testColorRangeSearch();
```

### 调试信息

所有操作都会在浏览器控制台输出详细日志：

- 颜色输入验证
- tt 命令执行结果
- 正则表达式生成过程
- 搜索结果统计

### 性能监控

- 搜索操作有超时保护（30秒）
- 批量操作有超时保护（2分钟）
- 支持中断正在进行的操作

## 配置选项

### 命令超时设置

```javascript
import { regexProcessor } from './regex_processor';

// 设置命令超时时间（毫秒）
regexProcessor.setCommandTimeout(60000); // 60秒
```

### 批量操作参数

默认批量操作参数：
- 首次间隔：10秒
- 其他间隔：2秒

可在代码中修改 `executeBatchOperation` 调用参数。

## 注意事项

1. **性能考虑**
   - 大型 `data.json` 文件可能影响搜索速度
   - 复杂正则表达式可能增加匹配时间

2. **安全考虑**
   - 批量操作会执行系统命令，请谨慎使用
   - 确认对话框可以防止误操作

3. **兼容性**
   - 需要外部 `tt` 工具支持
   - 依赖现有的颜色数据库功能

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本颜色范围搜索
- 支持按 target 分组显示
- 支持批量操作功能
- 完整的错误处理和用户反馈
