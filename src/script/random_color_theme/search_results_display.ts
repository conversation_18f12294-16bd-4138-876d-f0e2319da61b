/**
 * 搜索结果展示组件
 * 负责结果窗口的创建、target分组显示、批量操作等UI交互功能
 */

import { GroupedSearchResults, ColorSearchResult } from './color_range_search';
// import { messageError } from '../prompt_message'; // 暂时不需要，复制功能不显示错误消息
import { resetColorPickerState } from './color_picker_core';
import { messageSuccess } from '../prompt_message';

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns 是否复制成功
 */
async function copyToClipboard(text: string): Promise<boolean> {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            // 使用现代 Clipboard API
            await navigator.clipboard.writeText(text);
            return true;
        } else {
            // 降级到传统方法
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);
            return successful;
        }
    } catch (error) {
        console.error('复制到剪贴板失败:', error);
        return false;
    }
}

/**
 * 搜索结果展示管理器
 */
export class SearchResultsDisplay {
    private static instance: SearchResultsDisplay;
    private currentResultsWindow: HTMLElement | null = null;
    private currentAbortController: AbortController | null = null;

    private constructor() { }

    /**
     * 获取单例实例
     */
    static getInstance(): SearchResultsDisplay {
        if (!SearchResultsDisplay.instance) {
            SearchResultsDisplay.instance = new SearchResultsDisplay();
        }
        return SearchResultsDisplay.instance;
    }

    /**
     * 显示搜索结果
     * @param results 按target分组的搜索结果
     * @param originalColor 原始搜索的颜色值
     */
    showResults(results: GroupedSearchResults, originalColor: string): void {
        try {
            // 关闭取色器（如果正在运行）
            if (window._colorPickerActive) {
                resetColorPickerState();
            }

            // 关闭之前的结果窗口
            this.closeCurrentWindow();

            // 创建结果窗口
            this.currentResultsWindow = this.createResultsWindow(results, originalColor);

            // 添加到页面
            document.body.appendChild(this.currentResultsWindow);

            // 添加ESC键关闭事件
            this.setupKeyboardEvents();

        } catch (error) {
            console.error('显示搜索结果失败:', error);
            // 不显示错误消息，保持静默
        }
    }

    /**
     * 创建结果窗口 - 采用设备选择界面风格
     * @param results 搜索结果
     * @param originalColor 原始颜色值
     * @returns 结果窗口元素
     */
    private createResultsWindow(results: GroupedSearchResults, originalColor: string): HTMLElement {
        const overlay = document.createElement('div');
        overlay.className = 'color-search-results-overlay';

        const container = document.createElement('div');
        container.className = 'color-search-results-container';

        // 创建固定头部（标题 + 关闭按钮）
        const header = document.createElement('div');
        header.className = 'color-search-results-header';

        const title = this.createTitle(originalColor);
        const closeBtn = this.createCloseButton();

        header.appendChild(title);
        header.appendChild(closeBtn);
        container.appendChild(header);

        // 创建可滚动内容区域
        const content = document.createElement('div');
        content.className = 'color-search-results-content';

        // 创建target分组按钮
        const targetSections = this.createTargetSections(results);
        targetSections.forEach(section => content.appendChild(section));

        container.appendChild(content);

        overlay.appendChild(container);

        // 点击遮罩关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.closeCurrentWindow();
            }
        });

        return overlay;
    }

    /**
     * 创建标题 - 采用设备选择界面风格
     * @param originalColor 原始颜色值
     * @returns 标题元素
     */
    private createTitle(originalColor: string): HTMLElement {
        const title = document.createElement('div');
        title.className = 'color-search-results-title';
        title.textContent = `颜色范围搜索结果 - ${originalColor}`;
        return title;
    }

    /**
     * 创建关闭按钮 - 采用设备选择界面风格
     * @returns 关闭按钮元素
     */
    private createCloseButton(): HTMLElement {
        const closeBtn = document.createElement('button');
        closeBtn.textContent = '关闭';
        closeBtn.className = 'color-search-results-close';
        closeBtn.addEventListener('click', () => this.closeCurrentWindow());
        return closeBtn;
    }

    /**
     * 创建target分组 - 采用设备选择界面风格
     * @param results 搜索结果
     * @returns target分组元素数组
     */
    private createTargetSections(results: GroupedSearchResults): HTMLElement[] {
        const sections: HTMLElement[] = [];

        // 按target分组显示
        const targets = Object.keys(results).sort();

        for (const target of targets) {
            const targetSection = this.createTargetSection(target, results[ target ]);
            sections.push(targetSection);
        }

        return sections;
    }

    /**
     * 创建target分组区域 - 采用设备选择界面风格，添加直接执行按钮
     * @param target target名称
     * @param colors 该target下的颜色列表
     * @returns target区域元素
     */
    private createTargetSection(target: string, colors: ColorSearchResult[]): HTMLElement {
        const section = document.createElement('div');
        section.className = 'color-search-target-section';
        section.setAttribute('data-target', target);

        // 创建target信息按钮
        const header = document.createElement('button');
        header.className = 'color-search-target-header';
        header.addEventListener('click', async () => {
            // 添加加载状态指示
            const hint = section.querySelector('.color-search-target-hint') as HTMLElement;
            const originalText = hint?.textContent || '点击展开';
            if (hint) {
                hint.textContent = '加载中...';
            }

            try {
                await this.toggleTargetExpansion(section, colors);
            } catch (error) {
                console.error('展开颜色列表失败:', error);
                if (hint) {
                    hint.textContent = originalText;
                }
            }
        });

        const title = document.createElement('span');
        title.textContent = `${target} (${colors.length}个颜色)`;
        title.className = 'color-search-target-title';

        const clickHint = document.createElement('span');
        clickHint.textContent = '点击展开';
        clickHint.className = 'color-search-target-hint';

        header.appendChild(title);
        header.appendChild(clickHint);
        section.appendChild(header);

        // 分析颜色类型（添加防御性检查）
        const colorValues = colors.filter(c => c.data && c.data.name && !c.data.name.includes('.png'));
        const imageFiles = colors.filter(c => c.data && c.data.name && c.data.name.includes('.png'));

        // 创建颜色值复制按钮容器
        if (colorValues.length > 0) {
            const colorActionContainer = document.createElement('div');
            colorActionContainer.className = 'color-search-action-container';

            const colorLabel = document.createElement('div');
            colorLabel.className = 'color-search-action-label';

            // 只显示总数量
            colorLabel.innerHTML = `颜色值 (${colorValues.length}个)`;
            colorActionContainer.appendChild(colorLabel);

            const colorButtonsContainer = document.createElement('div');
            colorButtonsContainer.className = 'color-search-buttons-row';

            const colorButtons = this.createCopyButtonGroup(target, colorValues, 'color');
            colorButtons.forEach(btn => colorButtonsContainer.appendChild(btn));

            colorActionContainer.appendChild(colorButtonsContainer);
            section.appendChild(colorActionContainer);
        }

        // 创建图片复制按钮容器
        if (imageFiles.length > 0) {
            const imageActionContainer = document.createElement('div');
            imageActionContainer.className = 'color-search-action-container';

            const imageLabel = document.createElement('div');
            imageLabel.className = 'color-search-action-label';

            // 只显示总数量
            imageLabel.innerHTML = `图片 (${imageFiles.length}个)`;
            imageActionContainer.appendChild(imageLabel);

            const imageButtonsContainer = document.createElement('div');
            imageButtonsContainer.className = 'color-search-buttons-row';

            const imageButtons = this.createCopyButtonGroup(target, imageFiles, 'image');
            imageButtons.forEach(btn => imageButtonsContainer.appendChild(btn));

            imageActionContainer.appendChild(imageButtonsContainer);
            section.appendChild(imageActionContainer);
        }

        return section;
    }

    /**
     * 创建分段复制按钮组
     * @param target target名称
     * @param items 项目列表
     * @param type 按钮类型
     * @returns 按钮元素数组
     */
    private createCopyButtonGroup(target: string, items: ColorSearchResult[], type: 'color' | 'image'): HTMLElement[] {
        const buttons: HTMLElement[] = [];
        const totalCount = items.length;

        // 创建完整复制按钮（全部）
        const fullButton = this.createSingleCopyButton(
            `全部`,
            type,
            () => this.handleCopyCommand(target, items, type),
            true // 标记为完整复制按钮
        );
        buttons.push(fullButton);

        // 每20个为一组创建分段按钮
        const itemsPerSegment = 20;
        const segmentCount = Math.ceil(totalCount / itemsPerSegment);

        // 只有当总数超过20个时才创建分段按钮
        if (totalCount > itemsPerSegment) {
            for (let i = 0; i < segmentCount; i++) {
                const startIndex = i * itemsPerSegment;
                const endIndex = Math.min(startIndex + itemsPerSegment, totalCount);
                const segmentItems = items.slice(startIndex, endIndex);

                const startNumber = startIndex + 1;
                const endNumber = endIndex;

                const segmentButton = this.createSingleCopyButton(
                    `${startNumber}-${endNumber}`,
                    type,
                    () => this.handleCopyCommand(target, segmentItems, type)
                );
                buttons.push(segmentButton);
            }
        }

        return buttons;
    }




    /**
     * 创建单个复制按钮
     * @param text 按钮文字
     * @param type 按钮类型
     * @param onClick 点击事件
     * @param isFullCopy 是否为完整复制按钮
     * @returns 按钮元素
     */
    private createSingleCopyButton(text: string, type: 'color' | 'image', onClick: () => void, isFullCopy: boolean = false): HTMLElement {
        const button = document.createElement('button');
        let className = `color-search-action-btn color-search-action-${type}`;
        if (isFullCopy) {
            className += ' full-copy';
        }
        button.className = className;
        button.textContent = text;
        button.addEventListener('click', () => {
            // 添加点击过的样式
            button.classList.add('clicked');
            // 执行原始点击处理
            onClick();
        });
        return button;
    }

    /**
     * 切换target展开状态 - 采用设备选择界面风格，支持大列表异步渲染
     * @param section target区域元素
     * @param colors 颜色列表
     */
    private async toggleTargetExpansion(section: HTMLElement, colors: ColorSearchResult[]): Promise<void> {
        let colorList = section.querySelector('.color-search-color-list') as HTMLElement;

        if (!colorList) {
            // 首次展开，创建颜色列表
            colorList = document.createElement('div');
            colorList.className = 'color-search-color-list';

            // 先添加到DOM中，然后异步渲染内容
            section.appendChild(colorList);

            // 如果列表很大，使用分批渲染避免阻塞UI
            if (colors.length > 100) {
                await this.renderColorListBatch(colorList, colors);
            } else {
                // 小列表直接渲染
                for (const colorResult of colors) {
                    const colorItem = this.createColorItem(colorResult);
                    colorList.appendChild(colorItem);
                }
            }
        }

        // 切换展开状态
        colorList.classList.toggle('expanded');

        // 更新提示文字
        const hint = section.querySelector('.color-search-target-hint') as HTMLElement;
        if (hint) {
            hint.textContent = colorList.classList.contains('expanded') ? '点击收起' : '点击展开';
        }
    }

    /**
     * 分批渲染颜色列表，避免大列表阻塞UI
     * @param colorList 颜色列表容器
     * @param colors 颜色数据
     */
    private async renderColorListBatch(colorList: HTMLElement, colors: ColorSearchResult[]): Promise<void> {
        const batchSize = 50; // 每批渲染50个项目
        let index = 0;

        const renderBatch = () => {
            return new Promise<void>((resolve) => {
                const endIndex = Math.min(index + batchSize, colors.length);

                // 渲染当前批次
                for (let i = index; i < endIndex; i++) {
                    const colorItem = this.createColorItem(colors[ i ]);
                    colorList.appendChild(colorItem);
                }

                index = endIndex;

                if (index < colors.length) {
                    // 还有更多项目，使用requestAnimationFrame异步渲染下一批
                    requestAnimationFrame(() => {
                        renderBatch().then(resolve);
                    });
                } else {
                    // 全部渲染完成
                    resolve();
                }
            });
        };

        await renderBatch();
    }

    /**
     * 创建颜色项 - 采用设备选择界面风格，支持点击复制
     * @param colorResult 颜色结果
     * @returns 颜色项元素
     */
    private createColorItem(colorResult: ColorSearchResult): HTMLElement {
        const item = document.createElement('div');
        item.className = 'color-search-color-item';

        // 颜色预览
        const colorPreview = document.createElement('div');
        colorPreview.className = 'color-search-color-preview';
        colorPreview.style.backgroundColor = colorResult.color;

        // 颜色信息
        const colorInfo = document.createElement('div');
        colorInfo.className = 'color-search-color-info';

        const colorValue = document.createElement('span');
        colorValue.textContent = colorResult.color;
        colorValue.className = 'color-search-color-value clickable';
        colorValue.title = '点击复制颜色值';

        const colorName = document.createElement('span');
        colorName.textContent = colorResult.data?.name || '未知名称';
        colorName.className = 'color-search-color-name clickable';
        colorName.title = '点击复制资源名称';

        // 为颜色值添加点击复制功能
        colorValue.addEventListener('click', async (e) => {
            e.stopPropagation();
            try {
                await copyToClipboard(colorResult.color);
                messageSuccess('复制颜色值成功', 1000)
            } catch (error) {
                console.error('复制颜色值失败:', error);
            }
        });

        // 为资源名称添加点击复制功能
        colorName.addEventListener('click', async (e) => {
            e.stopPropagation();
            try {
                const resourceName = colorResult.data?.name;
                if (resourceName) {
                    await copyToClipboard(resourceName);
                    messageSuccess('复制资源名称成功', 1000)
                }
            } catch (error) {
                console.error('复制资源名称失败:', error);
            }
        });

        colorInfo.appendChild(colorValue);
        colorInfo.appendChild(colorName);

        item.appendChild(colorPreview);
        item.appendChild(colorInfo);

        return item;
    }

    // 已删除未使用的 handleTargetClick 方法

    /**
     * 处理复制命令到剪贴板
     * @param target target名称
     * @param colors 颜色列表
     * @param type 执行类型
     */
    private async handleCopyCommand(_target: string, colors: ColorSearchResult[], _type: 'color' | 'image'): Promise<void> {
        try {
            // 收集name值（添加防御性检查）
            const validColors = colors.filter(color => color.data && color.data.name);

            if (validColors.length === 0) {
                console.warn('没有找到有效的项目名称');
                return;
            }

            // 根据值的数量构建不同的命令格式
            let command: string;
            if (validColors.length === 1) {
                // 只有一个值时使用 tt 值 格式
                command = `tt ${validColors[ 0 ].data.name}`;
            } else {
                // 多个值时使用原有格式
                const nameList = validColors.map(color => color.data.name).join(',');
                command = `tt "${nameList}" 5 2`;
            }

            // 复制到剪贴板（静默操作，不显示消息提示）
            const success = await copyToClipboard(command);

            if (success) {
                console.log(`已复制命令到剪贴板: ${command}`);
            } else {
                console.error('复制失败，命令:', command);
            }

        } catch (error) {
            console.error('复制命令失败:', error);
        }
    }


    // 注意：原来的 stopExecution 和 toggleExecutionButtons 方法已移除
    // 因为现在使用复制命令功能，不再需要执行状态切换和中断功能

    // 已删除未使用的 executeBatchWithAbort 方法

    // 已删除复杂的 executeBatchWithProgress 方法，改用简化的 executeBatchOperation


    // 已删除不再使用的 parseProgressFromOutput 方法

    // 已删除废弃的 showConfirmDialog 方法

    /**
     * 设置键盘事件
     */
    private setupKeyboardEvents(): void {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                // 如果取色器正在运行，优先让取色器处理ESC键
                if (window._colorPickerActive) {
                    return; // 不处理，让取色器的ESC处理器处理
                }

                // 取色器未运行时，关闭搜索结果窗口
                this.closeCurrentWindow();
                document.removeEventListener('keydown', handleKeyDown);
            }
        };
        document.addEventListener('keydown', handleKeyDown);
    }

    /**
     * 关闭当前窗口
     */
    closeCurrentWindow(): void {
        if (this.currentResultsWindow) {
            document.body.removeChild(this.currentResultsWindow);
            this.currentResultsWindow = null;
        }

        // 中断正在进行的操作
        if (this.currentAbortController) {
            this.currentAbortController.abort();
            this.currentAbortController = null;
        }
    }
}

// 导出单例实例
export const searchResultsDisplay = SearchResultsDisplay.getInstance();

// 导出便捷函数
export function showSearchResults(results: GroupedSearchResults, originalColor: string): void {
    searchResultsDisplay.showResults(results, originalColor);
}

export function closeSearchResults(): void {
    searchResultsDisplay.closeCurrentWindow();
}
