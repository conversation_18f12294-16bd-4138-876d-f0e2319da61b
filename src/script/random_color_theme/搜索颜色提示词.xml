<!--
创建一个名为search_color的函数供前端调用，函数接受两个参数：
input_dir: 传入目录（所有截图文件的父目录）
data_path: 包含所有颜色映射表的JSON文件路径

函数实现以下功能：
1. 创建一个新窗口，在窗口横向并排显示input_dir目录下所有截图文件
   - 每个图片之间间距100px
   - 每张截图带有10px圆角并有投影
   - 图像从左至右，上下居中显示在窗口中间，如果两张图的数据完全相同，则不显示第二张图
   - 在截图列表末尾创建一个"添加截图"元素，点击后可截取手机当前界面并添加到列表

2. 颜色处理功能：
   - 使用Rust后端并行处理，为每张截图生成颜色表
   - 颜色表包含截图中所有去重后的颜色且在data.json中存在的颜色
   - 使用异步处理避免UI阻塞

3. 交互功能：
   - 鼠标点击截图时，获取点击位置的十六进制颜色值(#RRGGBB)
   - 根据data.json查找该颜色对应的name和target，并将name值复制到粘贴板中。在页面上方中间位置显示颜色信息。
   - 在所有截图中，相同颜色的位置上方添加红色高亮标记
   - 点击非配置颜色区域不触发任何高亮
   - 颜色匹配精度要求100%（RGB精确匹配）
   - 鼠标点击位置不在图片区域内不触发任何高亮

错误处理：
- 文件读取失败时显示错误信息但不阻塞其他截图的处理
- 颜色解析错误时记录日志但继续执行
- 窗口创建失败时提供友好的错误提示

性能优化：
- 使用Tauri的fs插件进行文件操作
- 图片处理采用多线程并行处理
- 大图片采用异步懒加载方式
- 颜色匹配使用哈希表加速查找

跨平台兼容性：
- 使用标准路径处理方法，确保路径在不同系统中正确
- 图片显示时考虑不同系统DPI差异

技术规范：
- 使用@tauri-apps/api/core的invoke方法调用后端
- 前端使用TypeScript实现UI和交互逻辑
- Rust后端负责高性能的颜色分析处理

data.json文件格式如下：
[
  {
    "color": "#7C79A3",
    "name": "divider_vertical_holo_light.9.png",
    "target": "framework-res"
  },
  {
    "color": "#355423",
    "name": "stat_sys_download_anim3.png",
    "target": "framework-res"
  },
  ...
]
-->