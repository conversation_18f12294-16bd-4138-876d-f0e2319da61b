<!--


创建一个名为process_theme的函数供前端调用，函数接受三个参数：
input_dir: 传入目录（所有反编译文件的父目录）
output_dir: 输出目录（主题包/完整主题包）
config_path: 配置文件（JSON5格式）

函数实现以下功能：

## 预处理阶段
1. 解析配置文件，将 delete_img、transparent_img、delete_color、transparent_color 等数组转换为哈希集合，使用 try-catch 捕获解析错误，出错时记录日志但继续执行
2. 将配置文件中带通配符的模式（如 .*内容.*）预编译为正则表达式对象，使用缓存避免重复编译
3. 定义一个空数组，用于存储一个字典，字典名称为 data_list
4. 预先生成大量不重复颜色，存入颜色池，用于后续快速获取唯一颜色

## 性能优化方案
1. 使用 Worker 线程池并行处理图片，避免阻塞主线程
2. 使用异步 IO 读写文件，减少 IO 等待时间
3. 使用内存映射文件处理大文件
4. 实现批处理机制，减少频繁的小文件操作
5. 使用内存缓存避免重复读取相同文件
6. 减少不必要的文件复制，优先使用硬链接或符号链接
7. 使用流式处理大型 XML 文件
8. 图片处理使用高效库，如 Sharp 替代普通图像处理库

## 错误处理机制
1. 所有操作遇到的错误只记录日志不中断流程

## 创建处理函数
创建一个图片处理函数，实现图片生成，函数接受一个哈希表，根据哈希表的 value 值执行不同的步骤，具体功能为：
    - 哈希表的 value 值为 png 时，读取哈希表的 key 值
        - 判断哈希表的 key 值中的图片是不是 .9.png 格式，
            - 是 .9.png 格式，则将图片的四周 1px 之外的不透明像素改为统一的随机不透明颜色
            - 不是 .9.png 格式，则将图片不透明像素改为统一的随机不透明颜色
    - 哈希表的 value 值为 xml 或 img 时
        - 生成一个 100*100px 的随机不重复颜色的图片，将生成的图片保存到 output_dir/子目录名/res/drawable-xxhdpi 目录下。生成的图片名为 key 值中最后的图片名，必须是png格式，不能是xml。
使用该函数处理的文件需要生成一个字典，字典格式如下：
{
    "color": "#生成的随机颜色（在push到字典时去除#ARGB中的A）",
    "name": "key值中最后的图片名（带后缀）",
    "target": "此图片在输出目录中的子目录名（输入目录下的子目录名）"
}，将字典 push 到 data_list 中。

创建一个颜色处理函数，函数接受一个目录，函数实现以下功能：
递归输出目录下所有子目录，找到所有 theme_values.xml 文件，并读取 xml 文件
    - 将 xml 中 color 标签的值替换为随机不重复的不透明颜色（格式为 #AARRGGBB）。
使用该函数修改的color标签的值将保存到字典数组中，字典格式如下：
{
    "color": "#生成的随机颜色（在push到字典时去除#ARGB中的A）",
    "name": "color标签的name值",
    "target": "theme_values.xml所在的子目录名称（即输出目录下的子目录名）"
}，将字典 push 到 data_list 中。

## 执行阶段（并行优化）
1. 遍历 input_dir 下所有子目录，创建 worker 任务队列，并行处理每个子目录
2. 对于每个子目录，并行执行以下操作：
   a. 遍历 res 目录下 drawable-xxhdpi、drawable-xhdpi、drawable-xxxhdpi、drawable 目录，使用 Promise.all 并行处理：
      - 将目录中 .png 文件复制到 output_dir/子目录名/res/drawable-xxhdpi 目录下，在复制时优先复制 drawable-xxhdpi 目录中的图片，如果 drawable-xxhdpi 目录下已经存在同名文件则不复制该文件
      - 将成功复制后的 png 图片记录到哈希表中，key 为复制后的图片完整路径+带后缀的文件名，value 为 png
        - 在记录到哈希表前，移除key中的所有 $ 符号，确保后缀名统一为.png
      - 将所有其他格式图片记录到哈希表中，key 为 output_dir/子目录名/res/drawable-xxhdpi/图片文件名（加后缀.png），value 为 img，只记录到哈希表不复制该文件到drawable-xxhdpi目录
        - 在记录到哈希表前，移除key中的所有 $ 符号，确保后缀名统一为.png
      - 处理目录中 xml 文件，如果 XML 文件内容中包含(</vector>, <vector, <shape, </shape>, <selector, </selector>,<path, color android:color, color android:color="@android:color") 任意字段：
            - 将该xml记录到哈希表中，key为output_dir/该xml所在的子目录名/res/drawable-xxhdpi/xml文件名（加后缀.png），value为xml。只记录到哈希表不复制该文件到drawable-xxhdpi目录
            - 在记录到哈希表前，移除key中的所有 $ 符号，确保后缀名统一为.png

   b. 异步处理 res/values/drawables.xml：遍历所有 <item> 标签，若 item 值非颜色值（不以 # 开头）且为资源引用（以 @ 开头）：
      - 提取资源名称（如 @drawable/icon 提取为 icon）
      - 将 item.name 记录在哈希表中，key 为 output_dir/子目录名/res/drawable-xxhdpi/item.name(带点.png后缀名)，value 为 xml。只记录到哈希表不复制该文件到drawable-xxhdpi目录
        - 在记录到哈希表前，移除key中的所有 $ 符号，确保后缀名统一为.png
      - 将 item.value 记录在哈希表中，key 为 output_dir/子目录名/res/drawable-xxhdpi/item.value(带点.png后缀名)，value 为 xml。只记录到哈希表不复制该文件到drawable-xxhdpi目录
        - 在记录到哈希表前，移除key中的所有 $ 符号，确保后缀名统一为.png

3. 处理配置文件中的 add_image（并行处理）：
   - 读取配置文件中的 add_image，add_image 是一个字典，键为子目录名（如 com.android.settings），值为图片文件名数组
   - 如果输出目录下有对应键名的子目录，则将 对应键值的图片记录到哈希表中，key 为 output_dir/键名/res/drawable-xxhdpi/键值（带.png后缀），value 为 img。只记录到哈希表不复制该文件到drawable-xxhdpi目录
     - 在记录到哈希表前，移除key中的所有 $ 符号，确保后缀名统一为.png

4. 以上步骤处理完成后对哈希表中记录的 key 去重，只保留一个。实现 worker 线程池并行调用图片处理函数处理图片。同时调用执行颜色处理函数处理颜色。

5. 上述操作完成后，使用多个工作线程并行递归遍历输出目录中所有图片和 theme_values.xml：
    对于图片：
    - 判断图片是否在 delete_img 数组，在 delete_img 数组中，则删除图片
    - 判断图片是否在 transparent_img 数组中，在 transparent_img 数组中，则将图片alpha通道改为0。
    - 判断图片名是否包含"transparent"，如果包含，则将图片alpha通道改为0。
    对于 theme_values.xml：
    - 判断 color 标签的 name 值是否在 delete_color 数组中，在 delete_color 数组中，则删除该 color 标签
    - 判断 color 标签的 name 值是否在 transparent_color 数组中，在 transparent_color 数组中，则将 color 标签的值改为 #00000000
    - 判断 color 标签的 name 值是否包含"transparent"，如果包含，则将 color 标签的值改为 #00000000

6. 将 data_list 中的数据保存到 output_dir的父目录/data.json 文件中，注意是输出目录的父目录。

7. 使用并行流处理创建输出目录的两个版本，并保存在output_dir的父目录下：
   - 颜色值版本（命名为"颜色值版本"）：去除对应子目录中的 res 目录，其他文件保持不变
   - 图片版本（命名为"图片版本"）：去除对应子目录中的 theme_values.xml，其他文件保持不变

## 进度报告与错误处理
1. 每一步操作打印耗时，处理完成后返回总耗时

注意：
1. 图片和 color 所有用到的颜色需确保唯一，不能重复
2. 所有文件操作使用异步 IO，减少等待时间
3. 路径处理需注意不同操作系统差异，使用 path 模块处理路径
4. 大文件使用流式处理，避免内存溢出
5. 使用内存缓存减少重复计算
7. 确保theme_values.xml中的颜色值为正确的ARGB格式（#AARRGGBB，且完全不透明）
8. 确保data.json中颜色值的target正确指向theme_values.xml所在的子目录名称
9. 代码数量要尽量简化。浓缩
-->