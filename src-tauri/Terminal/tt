#!/bin/bash

# 严格模式: 遇到错误立即退出, 使用未定义变量时报错, 管道中任一命令失败则整个管道失败
set -euo pipefail

# --- 配置与日志记录 ---
SCRIPT_NAME="tt"
log() {
    # 在数组模式中禁用输出
    if [ "${TT_ARRAY_MODE:-}" = "1" ]; then
        return
    fi
    # 检查消息是否以 "调试" 开头，如果是，则不打印
    if [[ "$1" == "调试"* ]]; then
        return
    fi
    echo "[$SCRIPT_NAME][$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# 函数: 检查命令是否存在且可执行
_check_command() {
    local cmd_name="$1"
    if ! command -v "$cmd_name" &>/dev/null; then
        # log "调试: 命令 '$cmd_name' 未找到 (command -v 失败)。" # 保留此行用于调试
        return 1 # 命令不存在
    fi
    if ! [ -x "$(command -v "$cmd_name")" ]; then
        # log "调试: 命令 '$cmd_name' 不可执行 (-x 测试失败)。" # 保留此行用于调试
        return 1 # 命令不可执行
    fi
    # log "调试: 命令 '$cmd_name' 存在且可执行。" # 保留此行用于调试
    return 0 # 命令存在且可执行
}

# 函数: 生成安全的十六进制范围
_generate_hex_range() {
    local min_val=$1
    local max_val=$2

    local min_hex=$(printf "%02X" $min_val)
    local max_hex=$(printf "%02X" $max_val)

    # 如果范围太大，使用通配符
    if [ $((max_val - min_val)) -gt 50 ]; then
        echo "[0-9A-F][0-9A-F]"
        return
    fi

    # 如果范围较小，生成枚举
    if [ $((max_val - min_val)) -le 10 ]; then
        local values=()
        for ((i=min_val; i<=max_val; i++)); do
            values+=($(printf "%02X" $i))
        done

        local pattern=""
        for val in "${values[@]}"; do
            if [ -z "$pattern" ]; then
                pattern="$val"
            else
                pattern="$pattern|$val"
            fi
        done
        echo "($pattern)"
        return
    fi

    # 中等范围，尝试智能分段
    local first_char_min=${min_hex:0:1}
    local first_char_max=${max_hex:0:1}
    local second_char_min=${min_hex:1:1}
    local second_char_max=${max_hex:1:1}

    # 如果第一位相同，只变化第二位
    if [ "$first_char_min" = "$first_char_max" ]; then
        echo "${first_char_min}[$second_char_min-$second_char_max]"
    else
        # 使用安全的范围表示
        echo "[$first_char_min-$first_char_max][0-9A-F]"
    fi
}

# 函数: 生成十六进制范围的OR表达式
_generate_hex_range() {
    local min_val="$1"
    local max_val="$2"

    # 如果范围相同，直接返回固定值
    if [ "$min_val" -eq "$max_val" ]; then
        printf "%02X" "$min_val"
        return
    fi

    # 对于小范围，使用枚举
    if [ $((max_val - min_val)) -le 15 ]; then
        local hex_list=""
        for (( i = min_val; i <= max_val; i++ )); do
            local hex_val=$(printf "%02X" "$i")
            if [ -z "$hex_list" ]; then
                hex_list="$hex_val"
            else
                hex_list="${hex_list}|${hex_val}"
            fi
        done
        echo "($hex_list)"
    else
        # 对于大范围，使用更智能的分组方式
        _generate_hex_range_groups "$min_val" "$max_val"
    fi
}

# 函数: 生成分组的十六进制范围
_generate_hex_range_groups() {
    local min_val="$1"
    local max_val="$2"

    local groups=""

    # 按十六进制的高位分组
    local current_group_start=$min_val

    while [ $current_group_start -le $max_val ]; do
        local current_group_end=$max_val
        local high_nibble=$((current_group_start / 16))
        local next_group_start=$(((high_nibble + 1) * 16))

        if [ $next_group_start -le $max_val ]; then
            current_group_end=$((next_group_start - 1))
        fi

        # 生成当前组的表达式
        local group_expr=""
        if [ $current_group_start -eq $((high_nibble * 16)) ] && [ $current_group_end -eq $(((high_nibble + 1) * 16 - 1)) ]; then
            # 完整的十六进制组 (例如: 20-2F)
            group_expr="$(printf "%X" $high_nibble)[0-9A-F]"
        else
            # 部分组，使用枚举
            for (( i = current_group_start; i <= current_group_end; i++ )); do
                local hex_val=$(printf "%02X" "$i")
                if [ -z "$group_expr" ]; then
                    group_expr="$hex_val"
                else
                    group_expr="${group_expr}|${hex_val}"
                fi
            done
            group_expr="($group_expr)"
        fi

        if [ -z "$groups" ]; then
            groups="$group_expr"
        else
            groups="${groups}|${group_expr}"
        fi

        current_group_start=$((current_group_end + 1))
    done

    echo "($groups)"
}

# 函数: 根据颜色值和偏差范围生成正则表达式
_generate_color_range_pattern() {
    local hex_color="$1"
    local range="$2"
    local r="$3"
    local g="$4"
    local b="$5"

    # 计算RGB范围
    local min_r=$(( r - (255 * range / 100) ))
    local max_r=$(( r + (255 * range / 100) ))
    local min_g=$(( g - (255 * range / 100) ))
    local max_g=$(( g + (255 * range / 100) ))
    local min_b=$(( b - (255 * range / 100) ))
    local max_b=$(( b + (255 * range / 100) ))

    # 边界处理
    min_r=$(( min_r < 0 ? 0 : min_r ))
    max_r=$(( max_r > 255 ? 255 : max_r ))
    min_g=$(( min_g < 0 ? 0 : min_g ))
    max_g=$(( max_g > 255 ? 255 : max_g ))
    min_b=$(( min_b < 0 ? 0 : min_b ))
    max_b=$(( max_b > 255 ? 255 : max_b ))

    # 生成各分量的正则表达式
    local r_pattern=$(_generate_hex_range "$min_r" "$max_r")
    local g_pattern=$(_generate_hex_range "$min_g" "$max_g")
    local b_pattern=$(_generate_hex_range "$min_b" "$max_b")

    # 组合成完整的正则表达式
    local pattern="FF${r_pattern}${g_pattern}${b_pattern}"
    local pattern_remove_ff="${r_pattern}${g_pattern}${b_pattern}"
    echo "rule = \"$pattern_remove_ff\""
}

# 函数: 生成正则表达式模式（兼容性函数）
_generate_regex_pattern() {
    # 这个函数现在只是为了兼容性，实际不应该被调用
    echo "警告: 使用了已弃用的函数 _generate_regex_pattern"
    echo "请使用 _generate_color_range_pattern 函数"
}

# 函数: 计算颜色变化范围 - RGB独立偏差组合
_calculate_color_variations() {
    local hex_color="$1"
    local range="$2"

    # 验证偏差范围参数
    if ! [[ "$range" =~ ^[0-9]+$ ]] || [ "$range" -lt 1 ] || [ "$range" -gt 30 ]; then
        echo "错误: 偏差范围必须是1-30之间的整数"
        return 1
    fi

    # 移除 # 号并转换为大写
    hex_color=$(echo "$hex_color" | sed 's/^#//' | tr '[:lower:]' '[:upper:]')

    # 验证十六进制颜色格式
    if ! [[ "$hex_color" =~ ^[0-9A-F]{6}$ ]]; then
        echo "错误: 无效的十六进制颜色格式。请使用6位格式，如 #FF0000 或 FF0000"
        return 1
    fi

    # 提取 RGB 分量
    local r=$((16#${hex_color:0:2}))
    local g=$((16#${hex_color:2:2}))
    local b=$((16#${hex_color:4:2}))

    # 根据颜色值和偏差范围生成精确的正则表达式
    _generate_color_range_pattern "$hex_color" "$range" "$r" "$g" "$b"
}

_check_dependencies() {
    local os_type=""
    if [[ "$(uname -s)" == "Darwin" ]]; then
        os_type="macos"
    elif [[ "$(uname -s)" == "Linux" ]]; then
        os_type="linux"
    else
        os_type="unknown" # 其他未知操作系统
    fi

    local missing_deps=()
    # 列出所有潜在的依赖项
    local dependencies=("zip" "realpath" "mktemp" "convert" "aapt")

    for cmd in "${dependencies[@]}"; do
        if ! command -v "$cmd" &>/dev/null; then
            missing_deps+=("$cmd")
        fi
    done

    if [ ${#missing_deps[@]} -gt 0 ]; then
        log "错误: 脚本运行所需的以下依赖项缺失:"
        for dep in "${missing_deps[@]}"; do
            log "  - $dep"
        done
        log "请安装这些依赖项并确保它们在您的 PATH 环境变量中，然后重试。"

        # 针对常见工具给出更具体的安装提示
        if [[ " ${missing_deps[*]} " =~ " convert " ]]; then
            if [[ "$os_type" == "macos" ]]; then
                log "提示 (macOS): 'convert' 是 ImageMagick 的一部分。您可以尝试使用 'brew install imagemagick' 来安装。"
            elif [[ "$os_type" == "linux" ]]; then
                log "提示 (Linux): 'convert' 是 ImageMagick 的一部分。您可以尝试使用 'sudo apt install imagemagick' (Debian/Ubuntu) 或 'sudo yum install ImageMagick' (CentOS/RHEL) 或您发行版对应的包管理器命令来安装。"
            else
                log "提示: 'convert' 是 ImageMagick 的一部分。请通过您系统的包管理器安装。"
            fi
        fi

        if [[ " ${missing_deps[*]} " =~ " aapt " ]]; then
            log "提示: 'aapt' 是 Android SDK Build-Tools 的一部分。请确保已安装并通过 Android Studio SDK 管理器或 Android SDK 命令行工具将其添加到 PATH。"
        fi

        local core_utils_needed_for_msg=false
        local zip_needed_for_msg=false

        # 检查是否需要提示 coreutils (realpath, mktemp)
        for dep in "${missing_deps[@]}"; do
            if [[ "$dep" == "realpath" ]] || [[ "$dep" == "mktemp" ]]; then
                core_utils_needed_for_msg=true
                break
            fi
        done

        # 检查是否需要提示 zip
        for dep in "${missing_deps[@]}"; do
            if [[ "$dep" == "zip" ]]; then
                zip_needed_for_msg=true
                break
            fi
        done

        if [[ "$core_utils_needed_for_msg" == true ]]; then
            if [[ "$os_type" == "macos" ]]; then
                log "提示 (macOS): 'realpath' 和 'mktemp' 通常是系统自带。如果缺失，'realpath' 可能通过 'brew install coreutils' 安装。"
            elif [[ "$os_type" == "linux" ]]; then
                log "提示 (Linux): 'realpath' 和 'mktemp' 通常包含在 'coreutils' 包中。您可以尝试使用 'sudo apt install coreutils' (Debian/Ubuntu) 或类似命令来安装。"
            else
                log "提示: 'realpath' 和 'mktemp' 通常是标准系统工具或通过 'coreutils' 包提供。"
            fi
        fi

        if [[ "$zip_needed_for_msg" == true ]]; then
            if [[ "$os_type" == "macos" ]]; then
                log "提示 (macOS): 'zip' 通常是系统自带。如果缺失，可以尝试 'brew install zip'。"
            elif [[ "$os_type" == "linux" ]]; then
                log "提示 (Linux): 'zip' 通常可以通过 'sudo apt install zip' (Debian/Ubuntu) 或类似命令来安装。"
            else
                log "提示: 'zip' 通常是标准系统工具。如果缺失，请检查您的系统配置或通过包管理器安装。"
            fi
        fi
        exit 1
    fi
}

# 在脚本早期执行依赖检查
_check_dependencies

DEFAULT_COLOR="FF0000" # 默认颜色值 (红色)

# --- 数组模式相关函数 ---

# 函数: 解析数组字符串
_parse_array() {
    local array_str="$1"

    # 只支持逗号分隔格式，不再支持方括号格式
    # 使用IFS分割字符串为数组
    local IFS=','
    local items=($array_str)

    # 清理每个元素的空格
    local cleaned_items=()
    for item in "${items[@]}"; do
        # 移除前后空格
        item=$(echo "$item" | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        if [ -n "$item" ]; then
            cleaned_items+=("$item")
        fi
    done

    # 返回清理后的数组
    printf '%s\n' "${cleaned_items[@]}"
}

# 函数: 检查是否为数组格式
_is_array_format() {
    local input="$1"
    # 只支持逗号分隔格式："a,b,c" (包含逗号的字符串)
    [[ "$input" =~ , ]]
}

# 函数: 执行单个颜色或图片操作
_execute_single_operation() {
    local resource_name="$1"
    local operation_type="$2"  # "color" 或 "image"
    local additional_param="$3" # 可选的额外参数（颜色值或图片路径）

    # 设置环境变量标记，防止递归进入数组模式
    export TT_SINGLE_MODE=1
    # 注意：不要清除TT_BATCH_MODE等批量处理相关的环境变量
    # 因为递归调用需要这些变量来正确生成批量内容

    # 构建命令参数
    local cmd_args=("$0" "$resource_name")
    if [ -n "$additional_param" ]; then
        cmd_args+=("$additional_param")
    fi

    # 执行命令（递归调用自身，但不会进入数组模式）
    "${cmd_args[@]}"
    local exit_code=$?

    # 只清除单次模式相关的环境变量
    unset TT_SINGLE_MODE

    return $exit_code
}

# 函数: 执行批量应用模式（首次应用）
_execute_batch_mode() {
    local items=("$@")
    local total_count=${#items[@]}

    # 分析项目类型
    local colors=()
    local images=()

    for item in "${items[@]}"; do
        if [[ "$item" == *.png || "$item" == *.9.png ]]; then
            images+=("$item")
        else
            colors+=("$item")
        fi
    done

    # 简化的批量处理：只处理第一种类型
    # 如果有颜色，就做颜色批量处理；如果只有图片，就做图片批量处理
    if [ ${#colors[@]} -gt 0 ]; then
        # 设置颜色批量模式环境变量
        export TT_BATCH_MODE=1
        export TT_ARRAY_MODE=1
        export TT_BATCH_COLORS="${colors[*]}"

        # 如果提供了自定义颜色，设置环境变量
        if [ -n "$custom_color" ]; then
            export TT_BATCH_CUSTOM_COLOR="$custom_color"
        fi

        # 调用单个颜色处理，但会生成包含所有颜色的XML
        # 如果有自定义颜色，将其作为第二个参数传递
        _execute_single_operation "${colors[0]}" "color" "${custom_color:-}" 2>/dev/null
        local result=$?

        # 清除环境变量
        unset TT_BATCH_MODE TT_ARRAY_MODE TT_BATCH_COLORS TT_BATCH_CUSTOM_COLOR

        return $result
    elif [ ${#images[@]} -gt 0 ]; then
        # 设置图片批量模式环境变量
        export TT_BATCH_MODE=1
        export TT_ARRAY_MODE=1
        export TT_BATCH_IMAGES="${images[*]}"

        # 调用单个图片处理，但会生成包含所有图片的文件夹
        _execute_single_operation "${images[0]}" "image" "" 2>/dev/null
        local result=$?

        # 清除环境变量
        unset TT_BATCH_MODE TT_ARRAY_MODE TT_BATCH_IMAGES

        return $result
    fi

    return 1  # 如果没有任何项目，返回失败
}


# 函数: 简约输出完成信息
_print_completion() {
    local current="$1"
    local total="$2"
    local status="$3"      # 状态：批量完成、单独完成等
    local item="${4:-}"    # 当前项目名

    printf "(%d/%d) %s" "$current" "$total" "$status"

    if [ -n "$item" ]; then
        printf ": %s" "$item"
    fi

    printf "\n"

    # 强制刷新输出缓冲区，确保实时显示
    # 使用 exec 重定向来强制刷新 stdout
    exec 1>&1
}

# 函数: 简单等待，不输出任何信息
_wait_silently() {
    local wait_seconds="$1"

    if [ "$wait_seconds" -eq 0 ]; then
        return 0  # 无需等待
    fi

    sleep "$wait_seconds"
    return 0
}

# 函数: 执行数组模式操作
_execute_array_mode() {
    local array_str="$1"
    local first_interval="${2:-10}"   # 首次间隔，默认10秒
    local other_interval="${3:-2}"    # 其余间隔，默认2秒
    local custom_color="$4"           # 可选的自定义颜色值

    # 如果第二个参数包含逗号，说明是旧格式的单一间隔时间
    if [[ "$first_interval" =~ , ]]; then
        # 旧格式兼容：第二个参数实际是custom_color
        custom_color="$first_interval"
        first_interval=10
        other_interval=2
    elif [[ "$other_interval" =~ , ]]; then
        # 第三个参数包含逗号，说明是custom_color
        custom_color="$other_interval"
        other_interval=2
    fi

    # 验证间隔时间
    if ! [[ "$first_interval" =~ ^[0-9]+$ ]] || [ "$first_interval" -lt 0 ]; then
        echo "错误: 首次间隔时间必须是非负整数"
        return 1
    fi
    if ! [[ "$other_interval" =~ ^[0-9]+$ ]] || [ "$other_interval" -lt 0 ]; then
        echo "错误: 其余间隔时间必须是非负整数"
        return 1
    fi

    # 解析数组
    local items=()
    while IFS= read -r line; do
        items+=("$line")
    done < <(_parse_array "$array_str")

    if [ ${#items[@]} -eq 0 ]; then
        echo "错误: 数组为空或格式无效"
        return 1
    fi

    local total_count=${#items[@]}
    local total_operations=$((total_count + 1))  # N+1次操作：1次批量 + N次单独

    # 在开始前打印等待时间信息
    if [ "$first_interval" -gt 0 ] || [ "$other_interval" -gt 0 ]; then
        echo "数组模式: ${total_count}个项目，首次间隔${first_interval}秒，其余间隔${other_interval}秒"
    else
        echo "数组模式: ${total_count}个项目，无间隔"
    fi

    # 强制刷新输出
    exec 1>&1

    local success_count=0

    # 第1次：批量应用所有元素
    if _execute_batch_mode "${items[@]}"; then
        success_count=$((success_count + 1))
        _print_completion "$success_count" "$total_operations" "首次" "包含所有值"
    else
        _print_completion "$success_count" "$total_operations" "首次" "包含所有值"
    fi

    # 首次间隔时间（批量处理后）
    if [ "$first_interval" -gt 0 ] && [ $success_count -lt $total_operations ]; then
        _wait_silently "$first_interval"
    fi

    # 第2-N+1次：逐个应用每个元素
    for i in "${!items[@]}"; do
        local item="${items[i]}"
        local current_operation=$((i + 2))  # 从第2次操作开始

        # 判断操作类型
        local operation_type="color"
        if [[ "$item" == *.png || "$item" == *.9.png ]]; then
            operation_type="image"
        fi

        # 执行单个操作（保留输出以支持实时进度显示）
        if _execute_single_operation "$item" "$operation_type" "${custom_color:-}" 2>/dev/null; then
            success_count=$((success_count + 1))
            _print_completion "$success_count" "$total_operations" "完成" "$item"
        else
            _print_completion "$success_count" "$total_operations" "失败" "$item"
        fi

        # 如果不是最后一个操作，则等待其余间隔时间
        if [ $current_operation -lt $total_operations ] && [ "$other_interval" -gt 0 ]; then
            _wait_silently "$other_interval"
        fi
    done

    # 显示最终完成状态
    if [ $success_count -eq $total_operations ]; then
        echo "✅ 全部完成 ($success_count/$total_operations)"
    else
        echo "⚠️ 部分完成 ($success_count/$total_operations)"
    fi

    # 如果所有操作都成功，返回0；否则返回1
    [ $success_count -eq $total_operations ]
}

# --- 帮助信息函数 ---
_print_usage() {
    cat <<EOF
用法:
  # 颜色计算模式 - 根据颜色值和偏差范围生成精确的正则表达式
  tt #FF0000 2        # 生成红色±2%偏差范围的正则表达式
  tt 3366CC 15        # 生成蓝色±15%偏差范围的正则表达式

  # 图片模式 - 将app中的图片替换为红色方块图片，图片名称为需要替换的图片名（支持.png和.9.png）
  tt imageName.png

  # 图片模式 - 将app中的图片替换为指定图片，图片名称为需要替换的图片名（支持.png和.9.png）
  tt imageName.png ./path/to/my_custom_image.png

  # 颜色模式 - 将app中的颜色值替换为不透明红色，颜色名称为需要替换的颜色名
  tt colorName

  # 颜色模式 - 将app中的颜色值替换为指定颜色值，颜色名称为需要替换的颜色名
  tt colorName 0000FF

  # 数组模式 - 执行N+1次操作：1次批量应用 + N次单独应用
  # 支持逗号分隔格式
  # 执行顺序：第1次批量应用所有元素，然后逐个单独应用每个元素

  # 逗号分隔格式（无shell扩展问题）
  tt "a,b,c,d,e"                       # 默认：首次间隔10秒，其余间隔2秒，使用默认红色
  tt "a,b,c,d,e" 5                     # 首次间隔5秒，其余间隔2秒，使用默认红色
  tt "a,b,c,d,e" 5 1                   # 首次间隔5秒，其余间隔1秒，使用默认红色
  tt "a.png,b.9.png,c.png" 0 0         # 无间隔快速执行
  tt "color1,color2" 15 3              # 首次间隔15秒，其余间隔3秒，使用默认红色
  tt "color1,color2" 10 2 0000FF       # 首次间隔10秒，其余间隔2秒，使用蓝色
  tt "color1,color2,color3" 0 0 00FF00 # 无间隔，使用绿色

注意:
  - 颜色计算模式的偏差范围必须是1-30之间的整数
  - 颜色值支持带或不带#号的6位十六进制格式
  - 根据输入颜色和偏差范围生成精确的正则表达式，而非通用通配符
  - 正则表达式会自动复制到剪贴板，可直接在VSCode中粘贴使用
  - 小范围偏差使用枚举模式，大范围偏差使用区间模式
  - 数组模式支持逗号分隔格式："a,b,c"
  - 数组模式会自动识别颜色和图片类型，无需额外指定
  - 不再支持方括号格式 '[a,b,c]'，仅支持逗号分隔格式
  - 数组模式支持自定义颜色：第4个参数可指定十六进制颜色值
  - 如果不指定颜色，批量模式默认使用红色 (FF0000)
  - 颜色参数支持6位 (RRGGBB) 或8位 (AARRGGBB) 十六进制格式
  - 数组模式执行策略：N+1次操作模式
    * 第1次：批量应用所有元素（生成所有标签并一次性应用）
    * 第2-N+1次：逐个单独应用每个元素
  - 支持分别设置首次间隔和其余间隔时间
  - 首次间隔：批量处理后的等待时间，默认10秒
  - 其余间隔：单个处理之间的等待时间，默认2秒
  - 间隔时间为非负整数（秒），0表示无间隔
  - 支持快捷键中断：按 Ctrl+C 停止脚本执行
EOF
}

# --- 帮助选项处理 ---
if [[ "$#" -eq 0 || "$1" == "-h" || "$1" == "--help" ]]; then
    _print_usage
    exit 0
fi

# --- 全局变量与初始化 ---
# --- Helper function to generate default images ---
_generate_default_image() {
    local output_path="$1"
    local is_nine_patch_flag="$2" # true or false

    if ! command -v convert &>/dev/null; then
        log "错误: 'convert' (ImageMagick) 命令未找到。生成默认图片需要 ImageMagick。"
        log "请安装 ImageMagick 并确保 'convert' 在您的 PATH 环境变量中。"
        return 1 # Using return code for function failure
    fi

    if [ "$is_nine_patch_flag" = true ]; then
        # 创建一个符合.9.png规范的简单九宫格图片
        # 创建一个红色方块，周围有1像素的透明边框
        if ! convert -size 102x102 xc:none -fill red -draw "rectangle 1,1 100,100" "$output_path" >/dev/null 2>&1; then
            log "错误: 使用 'convert' 生成九宫格图片失败。"
            return 1
        fi

        # 添加九宫格标记（上边和左边的黑色像素用于拉伸区域）
        if ! convert "$output_path" -fill black -draw "point 50,0 point 0,50" "$output_path" >/dev/null 2>&1; then
            log "错误: 使用 'convert' 添加九宫格标记失败。"
            return 1
        fi
    else
        if ! convert -size 100x100 xc:red "$output_path" >/dev/null 2>&1; then
            log "错误: 使用 'convert' 生成 .png 图片失败。"
            return 1
        fi
    fi
    return 0
}

# --- 参数定义与解析 ---
OPERATION_MODE="color"     # 'color' 或 'image'
RESOURCE_NAME=""           # $1: 颜色名称 或 目标图片文件名 (包内)
COLOR_HEX="$DEFAULT_COLOR" # 十六进制颜色值, 默认为 DEFAULT_COLOR

# 图片模式特定变量
# FINAL_IMAGE_NAME_IN_PACKAGE: 最终在 res/drawable-xxhdpi/ 中使用的原始图片文件名 (例如 my_image.png 或 my_image.9.png)
# 如果是 .9.png, aapt 会处理它并输出 .png。 description.xml 会引用编译后的 .png 文件名。
FINAL_IMAGE_NAME_IN_PACKAGE=""
# SOURCE_FOR_PROCESSING: 将被复制或被 aapt 处理的实际源图片文件的绝对路径 (用户提供或脚本生成)
SOURCE_FOR_PROCESSING=""
IS_NINE_PATCH_BEHAVIOR=false # 是否按 .9.png 行为处理 (影响 aapt 和默认图片生成)
XML_IMAGE_FILENAME=""        # 在 description.xml 中引用的图片文件名 (aapt 处理后的)
USER_PROVIDED_IMAGE_PATH=""  # 用户在第二个参数提供的原始图片路径
IMAGE_USER_FILENAME=""       # 从用户提供的路径或默认生成路径中提取的源图片文件名
IMAGE_PATH=""                # 在组件循环中使用的源图片路径，从 SOURCE_FOR_PROCESSING 获取

# --- 数组模式优先检测 ---
# 在参数数量校验之前先检查是否为数组模式
# 只有在非单一模式时才进入数组模式（避免递归）
if [ "${TT_SINGLE_MODE:-}" != "1" ] && _is_array_format "$1"; then
    # 数组模式允许最多4个参数：数组字符串、首次间隔、其余间隔、自定义颜色
    if [ "$#" -gt 4 ]; then
        log "错误: 数组模式参数数量不正确。"
        echo "用法:"
        echo "  逗号分隔格式（无shell扩展问题）"
        echo "  tt \"a,b,c,d,e\"                       # 默认：首次间隔10秒，其余间隔2秒，使用默认红色"
        echo "  tt \"a,b,c,d,e\" 5                     # 首次间隔5秒，其余间隔2秒，使用默认红色"
        echo "  tt \"a,b,c,d,e\" 5 1                   # 首次间隔5秒，其余间隔1秒，使用默认红色"
        echo "  tt \"a.png,b.9.png,c.png\" 0 0         # 无间隔快速执行"
        echo "  tt \"color1,color2\" 15 3              # 首次间隔15秒，其余间隔3秒，使用默认红色"
        echo "  tt \"color1,color2\" 10 2 0000FF       # 首次间隔10秒，其余间隔2秒，使用蓝色"
        echo "  tt \"color1,color2,color3\" 0 0 00FF00 # 无间隔，使用绿色"
        exit 1
    fi
    
    # 数组模式
    ARRAY_INPUT="$1"
    FIRST_INTERVAL="${2:-10}"    # 首次间隔，默认10秒
    OTHER_INTERVAL="${3:-2}"     # 其余间隔，默认2秒
    CUSTOM_COLOR="${4:-}"        # 可选的自定义颜色值

    _execute_array_mode "$ARRAY_INPUT" "$FIRST_INTERVAL" "$OTHER_INTERVAL" "$CUSTOM_COLOR"
    exit $?
fi

# --- 参数数量校验 ---
if [ "$#" -gt 3 ]; then
    log "错误: 参数数量不正确。"
    echo "用法:"
    echo "  注意: 如果参数包含特殊字符 (如 '#', ' ', '$' 等)，请使用引号将其括起来 (例如: '带空格的名称' 或 '#FF0000')。"
    echo "  颜色计算模式: tt <十六进制颜色值> <偏差范围1-30>"
    echo "    示例: tt FF0000 2"
    echo "    示例: tt '#3366CC' 3"
    echo "  颜色模式: tt <颜色名称> [十六进制颜色值]"
    echo "    示例: tt '颜色名称' FF0000"
    echo "    示例 (默认红色): tt '颜色名称'"
    echo "  图片模式: tt <目标图片文件名> <实际图片路径.png|.9.png>"
    echo "    示例 (使用指定图片): tt 'my_icon.png' '/path/to/actual_icon.png'"
    echo "    示例 (.9.png): tt 'my_stretch_bg.9.png' '/path/to/source.9.png'"
    echo "  图片模式 (生成默认图片): tt <目标图片文件名.png|.9.png>"
    echo "    示例 (生成默认 .png): tt 'my_default_icon.png'"
    echo "    示例 (生成默认 .9.png): tt 'my_default_bg.9.png'"
    exit 1
fi

RESOURCE_NAME="$1"
USER_PROVIDED_NAME="$RESOURCE_NAME" # 用户提供的原始名称，用于description.xml等

# --- 判断操作模式 ---
if [ "$#" -eq 1 ]; then
    # 单参数情况: $1 可能是颜色名或图片文件名
    ARG1_LOWER=$(echo "$RESOURCE_NAME" | tr '[:upper:]' '[:lower:]')

    if [[ "$ARG1_LOWER" == *.png || "$ARG1_LOWER" == *.9.png ]]; then
        OPERATION_MODE="image"
        FINAL_IMAGE_NAME_IN_PACKAGE="$RESOURCE_NAME"
        log "图片模式激活 (单参数，将生成默认图片)。"
        IMAGE_USER_FILENAME="$FINAL_IMAGE_NAME_IN_PACKAGE" # 源文件名基于目标包内名
        # SOURCE_FOR_PROCESSING 将在 _generate_default_image 中设置 (如果需要生成)
    else
        OPERATION_MODE="color" # 默认颜色已在 COLOR_HEX 初始化时设置
        log "颜色模式激活 (单参数，使用默认颜色 '$COLOR_HEX')。"
    fi
elif [ "$#" -eq 2 ]; then
    # 双参数情况: 可能是颜色计算模式、图片模式或颜色模式
    INPUT_VALUE="$2"
    INPUT_VALUE_LOWER=$(echo "$INPUT_VALUE" | tr '[:upper:]' '[:lower:]')

    # 检查是否是颜色计算模式 (第一个参数是十六进制颜色，第二个参数是1-30的数字)
    ARG1_CLEAN=$(echo "$RESOURCE_NAME" | sed 's/^#//')
    if [[ "$ARG1_CLEAN" =~ ^[0-9a-fA-F]{6}$ ]]; then
        if [[ "$INPUT_VALUE" =~ ^([1-9]|[12][0-9]|30)$ ]]; then
            # 颜色计算模式
            _calculate_color_variations "$RESOURCE_NAME" "$INPUT_VALUE"
            exit 0
        else
            # 第一个参数是颜色值，但第二个参数不是有效的偏差范围
            echo "错误: 检测到十六进制颜色值 '$RESOURCE_NAME'，但偏差范围 '$INPUT_VALUE' 无效。"
            echo "颜色计算模式的偏差范围必须是1-30之间的整数。"
            echo "用法: tt <十六进制颜色值> <偏差范围1-30>"
            echo "示例: tt FF0000 2"
            exit 1
        fi
    elif [[ "$INPUT_VALUE_LOWER" == *.png || "$INPUT_VALUE_LOWER" == *.9.png ]]; then
        OPERATION_MODE="image"
        FINAL_IMAGE_NAME_IN_PACKAGE="$RESOURCE_NAME" # 第一个参数是目标图片名
        USER_PROVIDED_IMAGE_PATH="$INPUT_VALUE"      # 第二个参数是源图片路径
        IMAGE_USER_FILENAME=$(basename "$USER_PROVIDED_IMAGE_PATH")
        log "图片模式激活 (双参数，使用用户提供图片)。"
    else
        OPERATION_MODE="color"
        COLOR_HEX="$INPUT_VALUE" # 第二个参数是颜色值
        log "颜色模式激活 (双参数，使用用户提供颜色值)。"
    fi
elif [ "$#" -eq 3 ]; then
    # 三参数情况: 检查是否是颜色计算模式 (tt hex <颜色值> <偏差范围>)
    if [ "$1" = "hex" ]; then
        # 颜色计算模式 (兼容旧格式)
        _calculate_color_variations "$2" "$3"
        exit 0
    else
        log "错误: 三参数模式只支持颜色计算功能。"
        echo "用法: tt <十六进制颜色值> <偏差范围1-30>"
        echo "示例: tt FF0000 2"
        echo "或者: tt hex FF0000 2 (兼容格式)"
        exit 1
    fi
fi

# --- 工作目录设置 ---
# 创建临时工作目录
WORK_DIR=$(mktemp -d "${TMPDIR:-/tmp}/theme_replace_temp.XXXXXXXXXX")
if [ ! -d "$WORK_DIR" ]; then
    log "错误: 无法创建临时工作目录。"
    exit 1
fi

# 设置 trap 以在脚本退出时清理临时目录
# shellcheck disable=SC2064 # WORK_DIR is expanded now, which is correct for the trap
trap "rm -rf '$WORK_DIR'" EXIT SIGINT SIGTERM

# --- 图片模式后续处理 (获取源图片路径, 检查aapt) ---
if [ "$OPERATION_MODE" == "image" ]; then
    # FINAL_IMAGE_NAME_IN_PACKAGE 应该在此之前已经被 $RESOURCE_NAME 赋值
    # 提取基本名称 (去除 .png 或 .9.png 后缀) 用于 XML 标签
    temp_basename="${FINAL_IMAGE_NAME_IN_PACKAGE%.9.png}"
    PROCESSED_THEME_NAME_FOR_XML_TAGS="${temp_basename%.png}"

    if [[ "$(echo "$FINAL_IMAGE_NAME_IN_PACKAGE" | tr '[:upper:]' '[:lower:]')" == *.9.png ]]; then
        IS_NINE_PATCH_BEHAVIOR=true
        XML_IMAGE_FILENAME="$(basename "$FINAL_IMAGE_NAME_IN_PACKAGE" .9.png).png"
        if ! command -v aapt &>/dev/null; then
            log "错误: 'aapt' 命令未找到。处理 .9.png 图片需要 aapt。"
            log "请安装 Android SDK Build-Tools 并确保 aapt 在您的 PATH 中。"
            exit 1
        fi
    else
        IS_NINE_PATCH_BEHAVIOR=false
        XML_IMAGE_FILENAME=$(basename "$FINAL_IMAGE_NAME_IN_PACKAGE")
    fi

    if [ -n "$USER_PROVIDED_IMAGE_PATH" ]; then # 如果用户提供了图片路径
        if [ ! -f "$USER_PROVIDED_IMAGE_PATH" ]; then
            log "错误: 用户提供的图片文件 '$USER_PROVIDED_IMAGE_PATH' 未找到。"
            exit 1
        fi
        SOURCE_FOR_PROCESSING=$(realpath "$USER_PROVIDED_IMAGE_PATH")
    else # 用户未提供图片路径 -> 将调用 _generate_default_image

        # 默认图片将生成在 WORK_DIR 内，并使用用户期望的最终包内文件名
        GENERATED_IMAGE_IN_WORK_DIR="$WORK_DIR/$FINAL_IMAGE_NAME_IN_PACKAGE"

        if ! _check_command "convert"; then
            log "错误: ImageMagick的 'convert' 命令未找到或不可执行，无法生成默认图片。"
            log "请安装 ImageMagick (例如，在 macOS 上使用 'brew install imagemagick'，在 Debian/Ubuntu 上使用 'sudo apt-get install imagemagick')。"
            exit 1
        fi

        if ! _generate_default_image "$GENERATED_IMAGE_IN_WORK_DIR" "$IS_NINE_PATCH_BEHAVIOR"; then
            log "错误: 生成默认图片失败。"
            exit 1
        fi
        SOURCE_FOR_PROCESSING="$GENERATED_IMAGE_IN_WORK_DIR"
        IMAGE_USER_FILENAME="$FINAL_IMAGE_NAME_IN_PACKAGE"
    fi

    if [ -n "$SOURCE_FOR_PROCESSING" ]; then
        IMAGE_PATH="$SOURCE_FOR_PROCESSING"
    elif [ "$OPERATION_MODE" == "image" ]; then # 如果是图片模式但 SOURCE_FOR_PROCESSING 仍为空，则出错
        log "错误 [内部]: 图片模式下 SOURCE_FOR_PROCESSING 未能正确设置。"
        exit 1
    fi
fi

# --- 颜色模式后续处理 (颜色值校验与格式化) ---
if [ "$OPERATION_MODE" == "color" ]; then

    PROCESSED_THEME_NAME_FOR_XML_TAGS=$(echo "$RESOURCE_NAME" | tr -d '[:space:]')

    # 如果是批量模式且有自定义颜色，使用自定义颜色
    if [ "${TT_BATCH_MODE:-}" = "1" ] && [ -n "${TT_BATCH_CUSTOM_COLOR:-}" ]; then
        COLOR_HEX="$TT_BATCH_CUSTOM_COLOR"
    fi

    TEMP_COLOR_VALUE=$(echo "$COLOR_HEX" | tr -d '[:space:]' | sed 's/^#//')
    if ! [[ "$TEMP_COLOR_VALUE" =~ ^[0-9a-fA-F]+$ ]]; then
        log "错误: 颜色值 '$TEMP_COLOR_VALUE' 包含非十六进制字符。"
        exit 1
    fi

    FORMATTED_HEX_COLOR=""
    if [ "${#TEMP_COLOR_VALUE}" -eq 6 ]; then # RRGGBB
        FORMATTED_HEX_COLOR="FF$(echo "$TEMP_COLOR_VALUE" | tr '[:lower:]' '[:upper:]')"
    elif [ "${#TEMP_COLOR_VALUE}" -eq 8 ]; then # AARRGGBB
        FORMATTED_HEX_COLOR="$(echo "$TEMP_COLOR_VALUE" | tr '[:lower:]' '[:upper:]')"
    else
        log "错误: 颜色值 '$TEMP_COLOR_VALUE' 长度无效。应为6位 (RRGGBB) 或8位 (AARRGGBB)。"
        exit 1
    fi
    COLOR_HEX="#${FORMATTED_HEX_COLOR}" # 更新 COLOR_HEX 为带#号的完整格式
    PROCESSED_COLOR_VALUE_FOR_XML="$COLOR_HEX"
fi

# MTZ 文件名基础 (基于RESOURCE_NAME, 替换空格)
MTZ_FILENAME_BASE=$(echo "$RESOURCE_NAME" | tr ' ' '_')

# --- (确保图片源) 如果是图片模式且未提供图片路径，则生成默认图片 ---
if [ "$OPERATION_MODE" == "image" ] && [ -z "$USER_PROVIDED_IMAGE_PATH" ]; then
    # 此时，USER_PROVIDED_IMAGE_PATH 为空, SOURCE_FOR_PROCESSING 也应该为空

    # 默认图片将生成在 WORK_DIR 内，并使用用户期望的最终包内文件名
    GENERATED_IMAGE_IN_WORK_DIR="$WORK_DIR/$FINAL_IMAGE_NAME_IN_PACKAGE"

    if ! _generate_default_image "$GENERATED_IMAGE_IN_WORK_DIR" "$IS_NINE_PATCH_BEHAVIOR"; then
        log "错误: 调用 _generate_default_image 生成默认图片失败。脚本中止。"
        exit 1
    fi

    # 将生成图片的路径设置为后续处理的源
    SOURCE_FOR_PROCESSING="$GENERATED_IMAGE_IN_WORK_DIR"
fi

# --- 创建 description.xml ---
DESCRIPTION_XML_PATH="$WORK_DIR/description.xml"
DESCRIPTION_TEXT_CONTENT=""
if [ "$OPERATION_MODE" == "color" ]; then
    DESCRIPTION_TEXT_CONTENT="由脚本生成的单一颜色主题。颜色名称: ${PROCESSED_THEME_NAME_FOR_XML_TAGS}, 颜色值: ${PROCESSED_COLOR_VALUE_FOR_XML}"
else # Image mode
    DESCRIPTION_TEXT_CONTENT="由脚本生成的图片主题。主题名/图片名: ${USER_PROVIDED_NAME}, 使用图片: ${PROCESSED_THEME_NAME_FOR_XML_TAGS}"
fi

cat <<EOF >"$DESCRIPTION_XML_PATH"
<?xml version="1.0" encoding="utf-8"?>
<Theme>
    <title>${USER_PROVIDED_NAME}</title>
    <designer>小米主题</designer>
    <author>小米主题</author>
    <version>1.0</version>
    <description>${DESCRIPTION_TEXT_CONTENT}</description>
    <uiVersion>9999</uiVersion>
    <miuiAdapterVersion>9999</miuiAdapterVersion>
</Theme>
EOF

# --- 定义目标组件列表 ---
COMPONENTS=(
    "com.android.contacts"
    "com.android.mms"
    "com.android.settings"
    "com.android.systemui"
    "com.miui.androidx.samples"
    "com.miui.home"
    "com.miui.securitycenter"
    "framework-miui-res"
    "framework-res"
    "miui.systemui.plugin"
)

# --- 创建内部组件包 (作为无.zip后缀的ZIP文件) ---
if ! command -v zip &>/dev/null; then
    log "错误: 'zip' 命令未找到。请确保已安装zip工具。"
    exit 1
fi

for COMPONENT_NAME in "${COMPONENTS[@]}"; do
    COMPONENT_CONTENT_DIR="$WORK_DIR/${COMPONENT_NAME}_content_temp"
    mkdir -p "$COMPONENT_CONTENT_DIR"

    COMPONENT_ZIP_TEMP_PATH="$WORK_DIR/${COMPONENT_NAME}_component_temp.zip" # 统一在 WORK_DIR 层面管理组件的临时 ZIP
    FINAL_COMPONENT_PATH_IN_WORK_DIR="$WORK_DIR/$COMPONENT_NAME"

    if [ "$OPERATION_MODE" == "color" ]; then
        THEME_VALUES_XML_PATH="$COMPONENT_CONTENT_DIR/theme_values.xml"

        # 检查是否为批量模式
        if [ "${TT_BATCH_MODE:-}" = "1" ] && [ -n "${TT_BATCH_COLORS:-}" ]; then
            # 批量模式：生成包含所有颜色的XML
            cat <<EOF >"$THEME_VALUES_XML_PATH"
<?xml version="1.0" encoding="utf-8"?>
<MIUI_Theme_Values>
EOF
            # 为每个颜色添加一个color标签
            IFS=' ' read -ra BATCH_COLOR_ARRAY <<< "$TT_BATCH_COLORS"
            for color_name in "${BATCH_COLOR_ARRAY[@]}"; do
                echo "    <color name=\"$color_name\">${PROCESSED_COLOR_VALUE_FOR_XML}</color>" >> "$THEME_VALUES_XML_PATH"
            done
            echo "</MIUI_Theme_Values>" >> "$THEME_VALUES_XML_PATH"
        else
            # 单个模式：只生成一个颜色
            cat <<EOF >"$THEME_VALUES_XML_PATH"
<?xml version="1.0" encoding="utf-8"?>
<MIUI_Theme_Values>
    <color name="${PROCESSED_THEME_NAME_FOR_XML_TAGS}">${PROCESSED_COLOR_VALUE_FOR_XML}</color>
</MIUI_Theme_Values>
EOF
        fi
        if ! zip -j "$COMPONENT_ZIP_TEMP_PATH" "$THEME_VALUES_XML_PATH" >/dev/null 2>&1; then
            log "错误 [颜色模式]: 为组件 $COMPONENT_NAME 创建临时ZIP包 (theme_values.xml) 失败。"
            rm -rf "$COMPONENT_CONTENT_DIR"
            exit 1
        fi
    else # Image mode
        TARGET_RES_DIR="$COMPONENT_CONTENT_DIR/res/drawable-xxhdpi"
        mkdir -p "$TARGET_RES_DIR"

        # 检查是否为批量模式
        if [ "${TT_BATCH_MODE:-}" = "1" ] && [ -n "${TT_BATCH_IMAGES:-}" ]; then
            # 批量模式：生成包含所有图片的文件夹
            IFS=' ' read -ra BATCH_IMAGE_ARRAY <<< "$TT_BATCH_IMAGES"

            # 生成一个基础图片（使用当前的IMAGE_PATH）
            local base_image_generated=false
            local base_image_path=""

            for image_name in "${BATCH_IMAGE_ARRAY[@]}"; do
                local target_image_name="$image_name"
                local is_nine_patch=false

                if [[ "$image_name" == *.9.png ]]; then
                    is_nine_patch=true
                    target_image_name="${image_name%.9.png}.png"
                fi

                local target_path="$TARGET_RES_DIR/$target_image_name"

                if [ "$base_image_generated" = false ]; then
                    # 第一个图片：生成或复制原始图片
                    if [ "$is_nine_patch" = true ]; then
                        if ! aapt s -i "$IMAGE_PATH" -o "$target_path" >/dev/null 2>&1; then
                            log "错误 [批量图片模式]: 'aapt s' 命令处理 '$IMAGE_PATH' 失败。"
                            rm -rf "$COMPONENT_CONTENT_DIR"
                            exit 1
                        fi
                    else
                        if ! cp "$IMAGE_PATH" "$target_path"; then
                            log "错误 [批量图片模式]: 复制 '$IMAGE_PATH' 失败。"
                            rm -rf "$COMPONENT_CONTENT_DIR"
                            exit 1
                        fi
                    fi
                    base_image_generated=true
                    base_image_path="$target_path"
                else
                    # 后续图片：复用第一个图片（硬链接以节省空间）
                    if ! ln "$base_image_path" "$target_path" 2>/dev/null; then
                        # 如果硬链接失败，则复制文件
                        if ! cp "$base_image_path" "$target_path"; then
                            log "错误 [批量图片模式]: 复用图片到 '$target_path' 失败。"
                            rm -rf "$COMPONENT_CONTENT_DIR"
                            exit 1
                        fi
                    fi
                fi
            done
        else
            # 单个模式：只处理一个图片
            TARGET_IMAGE_NAME_IN_RES="$FINAL_IMAGE_NAME_IN_PACKAGE"
            if [ "$IS_NINE_PATCH_BEHAVIOR" = true ]; then
                TARGET_IMAGE_NAME_IN_RES="${FINAL_IMAGE_NAME_IN_PACKAGE%.9.png}.png"
            fi
            TARGET_IMAGE_FINAL_PATH_IN_RES="$TARGET_RES_DIR/$TARGET_IMAGE_NAME_IN_RES"

            if [ "$IS_NINE_PATCH_BEHAVIOR" = true ]; then
                if ! aapt s -i "$IMAGE_PATH" -o "$TARGET_IMAGE_FINAL_PATH_IN_RES" >/dev/null 2>&1; then
                    log "错误 [图片模式]: 'aapt s' 命令为组件 $COMPONENT_NAME 处理 '$IMAGE_PATH' 失败。"
                    log "  源: '$IMAGE_PATH', 目标输出: '$TARGET_IMAGE_FINAL_PATH_IN_RES'"
                    rm -rf "$COMPONENT_CONTENT_DIR"
                    exit 1
                fi
            else
                if ! cp "$IMAGE_PATH" "$TARGET_IMAGE_FINAL_PATH_IN_RES"; then
                    log "错误 [图片模式]: 为组件 $COMPONENT_NAME 复制 '$IMAGE_PATH' 失败。"
                    rm -rf "$COMPONENT_CONTENT_DIR"
                    exit 1
                fi
            fi
        fi

        # ls -la "$COMPONENT_CONTENT_DIR"
        # ls -la "$TARGET_RES_DIR"

        # Zip the 'res' directory
        ( # 使用子 shell 来隔离 cd 的影响
            cd "$COMPONENT_CONTENT_DIR" || {
                log "严重错误: 无法进入目录 '$COMPONENT_CONTENT_DIR' 进行打包。"
                exit 1
            }
            # log "调试: 当前打包目录: $(pwd), 目标ZIP: '$COMPONENT_ZIP_TEMP_PATH'"
            if ! zip -r "$COMPONENT_ZIP_TEMP_PATH" res >/dev/null 2>&1; then
                # COMPONENT_ZIP_TEMP_PATH 是绝对路径，所以在这里直接使用是正确的
                log "错误 [图片模式]: 在 '$COMPONENT_CONTENT_DIR' 内为组件 $COMPONENT_NAME 创建临时ZIP包 (res directory) 失败。请检查zip命令兼容性或权限。"
                exit 1 # 子 shell 退出，表示打包失败
            fi
            # log "调试: ZIP 命令成功。"
        )
        # 检查子 shell (打包操作) 的退出状态
        if [ $? -ne 0 ]; then
            # 如果子 shell 因为 zip 失败或其他原因非正常退出 (例如 cd 失败)，这里会捕获到
            # log 消息应该已经在子 shell 中打印了，或者由 cd 的错误处理打印
            log "信息: 组件 $COMPONENT_NAME 的 ZIP 打包过程失败。"
            rm -rf "$COMPONENT_CONTENT_DIR" # 清理组件的临时内容目录
            # WORK_DIR 的清理由 trap 处理
            exit 1 # 主脚本退出
        fi
    fi

    if ! mv "$COMPONENT_ZIP_TEMP_PATH" "$FINAL_COMPONENT_PATH_IN_WORK_DIR"; then
        log "错误: 移动临时ZIP包 '$COMPONENT_ZIP_TEMP_PATH' 到 '$FINAL_COMPONENT_PATH_IN_WORK_DIR' 失败 (组件: $COMPONENT_NAME)."
        rm -rf "$COMPONENT_CONTENT_DIR"
        exit 1
    fi

    rm -rf "$COMPONENT_CONTENT_DIR"
done

OUTPUT_MTZ_FILENAME="单一颜色测试.mtz"
LOCAL_MTZ_PATH_IN_TEMP_DIR="$WORK_DIR/$OUTPUT_MTZ_FILENAME"

# 切换到工作目录，以便zip命令使用相对路径打包MTZ
pushd "$WORK_DIR" >/dev/null

# 如果已存在同名MTZ文件于临时目录，则删除
if [ -f "$OUTPUT_MTZ_FILENAME" ]; then
    if ! rm -f "$OUTPUT_MTZ_FILENAME"; then
        log "错误: 无法删除临时目录中已存在的MTZ文件: $OUTPUT_MTZ_FILENAME"
        popd >/dev/null
        exit 1
    fi
fi

# 准备要添加到MTZ文件列表
FILES_TO_ZIP_IN_MTZ=("description.xml")
for COMPONENT_NAME_IN_LOOP in "${COMPONENTS[@]}"; do # Renamed to avoid conflict with outer scope if any
    if [ -f "$COMPONENT_NAME_IN_LOOP" ]; then        # 确保组件包文件存在于 $WORK_DIR
        FILES_TO_ZIP_IN_MTZ+=("$COMPONENT_NAME_IN_LOOP")
    else
        log "警告: 组件包文件 $COMPONENT_NAME_IN_LOOP 在工作目录 $WORK_DIR 中未找到，将跳过。"
    fi
done

# 检查是否有文件可打包
if [ ${#FILES_TO_ZIP_IN_MTZ[@]} -le 1 ] && ([ ${#FILES_TO_ZIP_IN_MTZ[@]} -eq 0 ] || { [ "${FILES_TO_ZIP_IN_MTZ[0]}" == "description.xml" ] && [ ! -f "description.xml" ]; }); then
    log "错误: 没有有效的文件可打包到MTZ中 (description.xml 或组件包缺失)。"
    popd >/dev/null
    exit 1
fi

# 创建MTZ压缩包到临时目录中
if ! zip "$OUTPUT_MTZ_FILENAME" "${FILES_TO_ZIP_IN_MTZ[@]}" >/dev/null 2>&1; then # 静默zip的输出
    log "错误: 创建MTZ文件 $OUTPUT_MTZ_FILENAME 到临时目录 $WORK_DIR 失败。"
    popd >/dev/null
    exit 1
fi

popd >/dev/null # 返回到原始执行目录

# --- ADB 操作 ---
if ! command -v adb &>/dev/null; then
    log "错误: 'adb' 命令未找到。请确保已安装Android SDK Platform Tools并将其路径添加到环境变量中。跳过ADB操作。"
    exit 0
fi

# adb devices 输出格式:
# List of devices attached
# <serial_number>   device
# <serial_number>   offline
# ...
# 使用 awk 提取处于 'device' 状态的设备序列号
CONNECTED_DEVICES=$(adb devices | awk 'NF == 2 && $2 == "device" {print $1}')

if [ -z "$CONNECTED_DEVICES" ]; then
    exit 0
fi

# echo "$CONNECTED_DEVICES"

DEVICE_TARGET_PATH="/sdcard/Android/data/com.android.thememanager/files/temp.mtz"
ALL_DEVICES_SUCCESSFUL=true

for DEVICE_ID in $CONNECTED_DEVICES; do

    if ! adb -s "$DEVICE_ID" push "$LOCAL_MTZ_PATH_IN_TEMP_DIR" "$DEVICE_TARGET_PATH" >/dev/null 2>&1; then
        log "[设备 $DEVICE_ID] 错误: adb push 失败。请检查设备连接、存储空间和权限。"
        ALL_DEVICES_SUCCESSFUL=false
        continue # 继续处理下一台设备
    fi

    if ! adb -s "$DEVICE_ID" shell am start -n com.android.thememanager/com.android.thememanager.ApplyThemeForScreenshot -e theme_file_path "$DEVICE_TARGET_PATH" -e api_called_from ThemeEditor >/dev/null 2>&1; then
        log "[设备 $DEVICE_ID] 警告: adb shell am start 命令执行失败或返回非零退出码。主题可能未成功应用。"
        ALL_DEVICES_SUCCESSFUL=false
    fi
done

exit 0
